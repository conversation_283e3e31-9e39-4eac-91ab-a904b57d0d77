﻿using Microsoft.Graph;
using Microsoft.Graph.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace PharmaLex.Authentication.B2C
{
    public static class UserExtensions
    {
        public static string GetEmail(this User user)
        {
            if (!string.IsNullOrEmpty(user.UserPrincipalName))
            {
                //We don't retrieve UPN from b2c, if it exists then it is a pharmalex AD user
                return user.UserPrincipalName;
            }

            var email = user.Identities?.Find(x => x.SignInType == "emailAddress" || x.SignInType == "userPrincipalName")?.IssuerAssignedId;

            return email ?? user.Mail;
        }

        //this method is no more extension method after upgradation of microsoft.graph this method is modified.
        public static async Task<List<User>> GetAllAsync(UserCollectionResponse users, GraphServiceClient client)
        {
            List<User> allusers = new List<User>();
            while (users.Value != null)
            {
                allusers.AddRange(users.Value);
                // If OdataNextLink has a value, there is another page
                if (!string.IsNullOrEmpty(users.OdataNextLink))
                {
                    // Pass the OdataNextLink to the WithUrl method to request the next page
                    users = await client.Users
                        .WithUrl(users.OdataNextLink)
                        .GetAsync();
                    allusers.AddRange(users.Value);
                }
                else
                {
                    // No more results, exit loop
                    break;
                }
            }
            return allusers;
        }
    }
}
