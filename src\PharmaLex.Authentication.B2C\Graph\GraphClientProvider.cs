﻿using Azure.Identity;
using Microsoft.Extensions.Options;
using Microsoft.Graph;

namespace PharmaLex.Authentication.B2C
{
    public interface IGraphClientProvider<T> where T : GraphOptions
    {
        GraphServiceClient Client { get; }
        string Tenant { get; }
    }

    public abstract class GraphClientProvider<T> : IGraphClientProvider<T> where T : GraphOptions
    {
        private readonly GraphOptions options;
        private ClientSecretCredential _clientSecretCredential;
        private GraphServiceClient _appClient;
        public GraphClientProvider(IOptions<T> options)
        {
            this.options = options.Value;
        }

        public string Tenant => this.options.Domain;
        public GraphServiceClient Client
        {
            get
            {

                if (_clientSecretCredential == null)
                {
                    _clientSecretCredential = new ClientSecretCredential(
                    this.options.Domain, this.options.ClientId, this.options.ClientSecret);
                }

                if (_appClient == null)
                {
                    _appClient = new GraphServiceClient(_clientSecretCredential);
                }

                return _appClient;
            }
        }
    }
}
