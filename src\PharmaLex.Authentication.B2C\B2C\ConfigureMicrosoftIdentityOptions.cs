﻿using Microsoft.AspNetCore.Authentication.OpenIdConnect;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using Microsoft.Identity.Web;

namespace PharmaLex.Authentication.B2C
{
    public class ConfigureMicrosoftIdentityOptions : IConfigureNamedOptions<OpenIdConnectOptions>
    {
        private readonly IConfiguration configuration;
        private readonly IIdentityProviderEventCallbacks identityProviderEvents;

        public ConfigureMicrosoftIdentityOptions(IConfiguration configuration,
            IIdentityProviderEventCallbacks identityProviderEvents)
        {
            this.configuration = configuration;
            this.identityProviderEvents = identityProviderEvents;
        }

        public void Configure(string name, OpenIdConnectOptions options)
        {
            this.configuration.GetSection("AzureAdB2C").Bind(options);
            options.TokenValidationParameters.NameClaimType = ClaimConstants.Email;

            options.Events.OnTicketReceived = async context => await this.identityProviderEvents.OnTicketReceived(context);
            options.Events.OnRedirectToIdentityProvider = async context => await this.identityProviderEvents.OnRedirectToIdentityProvider(context);
        }
        public void Configure(OpenIdConnectOptions options)
        {
            Configure(Options.DefaultName, options);
        }
    }
}