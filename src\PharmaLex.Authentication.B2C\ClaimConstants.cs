﻿namespace PharmaLex.Authentication.B2C
{
    public static class ClaimConstants
    {
        public const string AuthenticationTime = "auth_time";
        public const string Policy = "tfp";
        public const string Email = "emails";
        public const string Name = "name";
        public const string GivenName = "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname";
        public const string FamilyName = "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/surname";
        public const string IdentityProvider = "http://schemas.microsoft.com/identity/claims/identityprovider";
    }

    public static class UserFlows
    {
        public const string SignInLocal = "b2c_1_signin_local";
        public const string SignInFederated = "b2c_1_signin_federated";
        public const string SignUpSignInCombined = "b2c_1_signup_signin_plx";
        public const string PasswordResetLocal = "b2c_1_password_reset";
        public const string ProfileEditLocal = "b2c_1_profile_edit";
    }
}
