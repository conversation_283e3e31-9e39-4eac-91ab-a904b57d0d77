﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.8.34330.188
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.Authentication.B2C", "src\PharmaLex.Authentication.B2C\PharmaLex.Authentication.B2C.csproj", "{745A629C-02A0-4AC7-B8F5-29E0724FD32A}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.Authentication.B2C.Tests", "test\PharmaLex.Authentication.B2C.Tests\PharmaLex.Authentication.B2C.Tests.csproj", "{79001D2C-A7B5-47A5-811A-07A269B129DD}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "test", "test", "{25B648D2-A6DD-43E5-AFA3-C8DD8FEB9079}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{597214EA-6423-46B1-82C7-B5D6330C23B3}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "config", "config", "{3392A446-AC95-4823-8C81-FC8965F0FE18}"
	ProjectSection(SolutionItems) = preProject
		.gitignore = .gitignore
		azure-pipelines.yml = azure-pipelines.yml
		README.md = README.md
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{745A629C-02A0-4AC7-B8F5-29E0724FD32A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{745A629C-02A0-4AC7-B8F5-29E0724FD32A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{745A629C-02A0-4AC7-B8F5-29E0724FD32A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{745A629C-02A0-4AC7-B8F5-29E0724FD32A}.Release|Any CPU.Build.0 = Release|Any CPU
		{79001D2C-A7B5-47A5-811A-07A269B129DD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{79001D2C-A7B5-47A5-811A-07A269B129DD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{79001D2C-A7B5-47A5-811A-07A269B129DD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{79001D2C-A7B5-47A5-811A-07A269B129DD}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{745A629C-02A0-4AC7-B8F5-29E0724FD32A} = {597214EA-6423-46B1-82C7-B5D6330C23B3}
		{79001D2C-A7B5-47A5-811A-07A269B129DD} = {25B648D2-A6DD-43E5-AFA3-C8DD8FEB9079}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {5299DAA7-341D-4F77-AD94-5A2A7C79572A}
	EndGlobalSection
EndGlobal
