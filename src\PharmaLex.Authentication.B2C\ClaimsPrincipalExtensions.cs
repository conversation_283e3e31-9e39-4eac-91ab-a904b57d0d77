﻿using System;
using System.Linq;
using System.Security.Claims;
using System.Security.Principal;

namespace PharmaLex.Authentication.B2C
{
    public static class ClaimsPrincipalExtensions
    {
        public static bool IsSuperAdmin(this ClaimsPrincipal cp)
        {
            return cp.HasClaim(x => x.Type == "admin:SuperAdmin");
        }

        public static bool IsPharmaLexUser(this ClaimsPrincipal user)
        {
            return (user.Identity as ClaimsIdentity).GetClaimValue(ClaimConstants.IdentityProvider) == "https://login.microsoftonline.com/ff9ac3ce-3c41-41c3-b556-e1b32a662fed/v2.0";
        }

        public static bool IsLocalUser(this ClaimsPrincipal user)
        {
            return !user.HasClaim(x => x.Type == ClaimConstants.IdentityProvider);
        }

        public static string GetEmail(this ClaimsIdentity user)
        {
            return user.GetClaimValue(ClaimConstants.Email)?.ToLower();
        }

        public static string GetEmail(this ClaimsPrincipal user)
        {
            return (user.Identity as ClaimsIdentity).GetEmail();
        }

        public static string GetEmail(this IIdentity user)
        {
            return (user as ClaimsIdentity).GetEmail();
        }

        public static string GetName(this ClaimsPrincipal user)
        {
            return $"{user.GetClaimValue(ClaimConstants.GivenName)} {user.GetClaimValue(ClaimConstants.FamilyName)}";
        }

        public static T GetClaimValue<T>(this ClaimsIdentity user, string claimType) where T : struct
        {
            string val = user.Claims.FirstOrDefault(z => z.Type == claimType)?.Value;

            if (string.IsNullOrEmpty(val))
                return default(T);

            return (T)Convert.ChangeType(val, typeof(T));
        }
        public static T GetClaimValue<T>(this IIdentity user, string claimType) where T : struct
        {
            return (user as ClaimsIdentity).GetClaimValue<T>(claimType);
        }
        public static T GetClaimValue<T>(this ClaimsPrincipal user, string claimType) where T : struct
        {
            return (user.Identity as ClaimsIdentity).GetClaimValue<T>(claimType);
        }

        public static string GetClaimValue(this ClaimsIdentity user, string claimType)
        {
            return user.Claims.FirstOrDefault(z => z.Type == claimType)?.Value;
        }

        public static string GetClaimValue(this IIdentity user, string claimType)
        {
            return (user as ClaimsIdentity).GetClaimValue(claimType);
        }

        public static string GetClaimValue(this ClaimsPrincipal user, string claimType)
        {
            return (user.Identity as ClaimsIdentity).GetClaimValue(claimType);
        }
    }
}
