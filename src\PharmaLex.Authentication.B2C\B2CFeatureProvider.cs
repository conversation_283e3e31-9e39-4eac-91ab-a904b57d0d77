﻿using Microsoft.AspNetCore.Mvc.ApplicationParts;
using Microsoft.AspNetCore.Mvc.Controllers;
using PharmaLex.Authentication.B2C.Controllers;
using System.Collections.Generic;
using System.Reflection;

namespace PharmaLex.Authentication.B2C
{
    internal class B2CFeatureProvider : IApplicationFeatureProvider<ControllerFeature>
    {
        public void PopulateFeature(IEnumerable<ApplicationPart> parts, ControllerFeature feature)
        {
            if (!feature.Controllers.Contains(typeof(OidcController).GetTypeInfo()))
            {
                feature.Controllers.Add(typeof(OidcController).GetTypeInfo());
            }
        }
    }
}