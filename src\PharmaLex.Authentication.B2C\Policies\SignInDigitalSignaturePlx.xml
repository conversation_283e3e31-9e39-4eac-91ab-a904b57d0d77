﻿<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<TrustFrameworkPolicy
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns:xsd="http://www.w3.org/2001/XMLSchema"
  xmlns="http://schemas.microsoft.com/online/cpim/schemas/2013/06"
  PolicySchemaVersion="*******"
  TenantId="smartphlexb2c.onmicrosoft.com"
  PolicyId="B2C_1A_signin_signature_plx"
  DeploymentMode="Development"
  UserJourneyRecorderEndpoint="urn:journeyrecorder:applicationinsights"
  PublicPolicyUri="http://smartphlexb2c.onmicrosoft.com/B2C_1A_signin_signature_plx">

  <BasePolicy>
    <TenantId>smartphlexb2c.onmicrosoft.com</TenantId>
    <PolicyId>B2C_1A_TrustFrameworkExtensions</PolicyId>
  </BasePolicy>
  <BuildingBlocks>
    <ClaimsSchema>
      <!-- Read-only email address to present to the user-->
      <ClaimType Id="ReadOnlyEmail">
        <DisplayName>Email Address</DisplayName>
        <DataType>string</DataType>
        <UserInputType>Readonly</UserInputType>
      </ClaimType>

      <!-- Stores the error message for unsolicited request (a request without id_token_hint) and user not found-->
      <ClaimType Id="errorMessage">
        <DisplayName>Error</DisplayName>
        <DataType>string</DataType>
        <UserHelpText>Add help text here</UserHelpText>
        <UserInputType>Paragraph</UserInputType>
      </ClaimType>
    </ClaimsSchema>

    <ClaimsTransformations>
      <!-- Initiates the errorMessage claims type with the error message-->
      <ClaimsTransformation Id="CreateUnsolicitedErrorMessage" TransformationMethod="CreateStringClaim">
        <InputParameters>
          <InputParameter Id="value" DataType="string" Value="Signed-in user email is required" />
        </InputParameters>
        <OutputClaims>
          <OutputClaim ClaimTypeReferenceId="errorMessage" TransformationClaimType="createdClaim" />
        </OutputClaims>
      </ClaimsTransformation>

      <!-- Copy the email to ReadOnlyEamil claim type-->
      <ClaimsTransformation Id="CopyEmailAddress" TransformationMethod="FormatStringClaim">
        <InputClaims>
          <InputClaim ClaimTypeReferenceId="signInName" TransformationClaimType="inputClaim" />
        </InputClaims>
        <InputParameters>
          <InputParameter Id="stringFormat" DataType="string" Value="{0}" />
        </InputParameters>
        <OutputClaims>
          <OutputClaim ClaimTypeReferenceId="ReadOnlyEmail" TransformationClaimType="outputClaim" />
        </OutputClaims>
      </ClaimsTransformation>
    </ClaimsTransformations>

    <!-- Set the custom UI assets -->
    <ContentDefinitions>
      <ContentDefinition Id="api.selfasserted">
        <LoadUri>https://plxstaticcontent.blob.core.windows.net/$web/b2c/smartphlex-b2c.html</LoadUri>
      </ContentDefinition>
    </ContentDefinitions>
  </BuildingBlocks>

  <ClaimsProviders>
    <!--<ClaimsProvider>
      <DisplayName>Local Account</DisplayName>
      <TechnicalProfiles>
        <TechnicalProfile Id="LocalAccountSignatureEmail">
          <DisplayName>Local Account Signin</DisplayName>
          <Protocol Name="Proprietary" Handler="Web.TPEngine.Providers.SelfAssertedAttributeProvider, Web.TPEngine, Version=*******, Culture=neutral, PublicKeyToken=null" />
          <Metadata>
            <Item Key="SignUpTarget">SignUpWithLogonEmailExchange</Item>
            <Item Key="setting.operatingMode">Email</Item>
            <Item Key="setting.showCancelButton">false</Item>
            <Item Key="ContentDefinitionReferenceId">api.selfasserted</Item>
          </Metadata>
          <InputClaimsTransformations>
            <InputClaimsTransformation ReferenceId="CopyEmailAddress" />
          </InputClaimsTransformations>
          <InputClaims>
            <InputClaim ClaimTypeReferenceId="ReadOnlyEmail" />
          </InputClaims>
          <OutputClaims>
            <OutputClaim ClaimTypeReferenceId="ReadOnlyEmail" Required="true" />
            <OutputClaim ClaimTypeReferenceId="password" Required="true" />
            <OutputClaim ClaimTypeReferenceId="objectId" />
            <OutputClaim ClaimTypeReferenceId="authenticationSource" />
          </OutputClaims>
          <ValidationTechnicalProfiles>
            <ValidationTechnicalProfile ReferenceId="login-NonInteractive" />
          </ValidationTechnicalProfiles>
          <UseTechnicalProfileForSessionManagement ReferenceId="SM-Noop" />
        </TechnicalProfile>
      </TechnicalProfiles>
    </ClaimsProvider>-->

	  <ClaimsProvider>
		  <Domain>pharmalex.com</Domain>
		  <DisplayName>Login with PharmaLex</DisplayName>
		  <TechnicalProfiles>
			  <TechnicalProfile Id="PharmaLex-OpenIdConnect">
				  <DisplayName>PharmaLex</DisplayName>
				  <Description>Login with your PharmaLex account</Description>
				  <Protocol Name="OpenIdConnect"/>
				  <Metadata>
					  <Item Key="METADATA">https://login.microsoftonline.com/yes-services.eu/v2.0/.well-known/openid-configuration</Item>
					  <Item Key="client_id">c4651a17-8422-4fee-b3e2-31536f43a0e8</Item>
					  <Item Key="response_types">code</Item>
					  <Item Key="scope">openid profile</Item>
					  <Item Key="response_mode">form_post</Item>
					  <Item Key="HttpBinding">POST</Item>
					  <Item Key="UsePolicyInRedirectUri">false</Item>
				  </Metadata>
				   <CryptographicKeys>
                        <Key Id="client_secret" StorageReferenceId="B2C_1A_PharmaLexSecret"/>
                    </CryptographicKeys>
				  <InputClaimsTransformations>
					  <InputClaimsTransformation ReferenceId="CopyEmailAddress" />
				  </InputClaimsTransformations>
				  <InputClaims>
					  <InputClaim ClaimTypeReferenceId="ReadOnlyEmail"/>
					  <InputClaim ClaimTypeReferenceId="signInName"/>
				  </InputClaims>
				  <OutputClaims>
					  <OutputClaim ClaimTypeReferenceId="issuerUserId" PartnerClaimType="oid"/>
					  <OutputClaim ClaimTypeReferenceId="tenantId" PartnerClaimType="tid"/>
					  <OutputClaim ClaimTypeReferenceId="givenName" PartnerClaimType="given_name" />
					  <OutputClaim ClaimTypeReferenceId="surName" PartnerClaimType="family_name" />
					  <OutputClaim ClaimTypeReferenceId="displayName" PartnerClaimType="name" />
					  <OutputClaim ClaimTypeReferenceId="email" PartnerClaimType="email" />
					  <OutputClaim ClaimTypeReferenceId="authenticationSource" DefaultValue="socialIdpAuthentication" AlwaysUseDefaultValue="true" />
					  <OutputClaim ClaimTypeReferenceId="identityProvider" PartnerClaimType="iss" />
					  <OutputClaim ClaimTypeReferenceId="ReadOnlyEmail" Required="true" />
					  <OutputClaim ClaimTypeReferenceId="objectId" />
					  <OutputClaim ClaimTypeReferenceId="otherMails" />
					  <OutputClaim ClaimTypeReferenceId="signInNames.emailAddress" />
				  </OutputClaims>
				  <OutputClaimsTransformations>
					  <OutputClaimsTransformation ReferenceId="CreateRandomUPNUserName"/>
					  <OutputClaimsTransformation ReferenceId="CreateUserPrincipalName"/>
					  <OutputClaimsTransformation ReferenceId="CreateAlternativeSecurityId"/>
					  <OutputClaimsTransformation ReferenceId="CreateSubjectClaimFromAlternativeSecurityId"/>
				  </OutputClaimsTransformations>

				  <UseTechnicalProfileForSessionManagement ReferenceId="SM-SocialLogin"/>
			  </TechnicalProfile>
		  </TechnicalProfiles>
	  </ClaimsProvider>

    <!--<ClaimsProvider>
      <DisplayName>Self Asserted</DisplayName>
      <TechnicalProfiles>
        --><!-- Show error message--><!--
        <TechnicalProfile Id="SelfAsserted-Unsolicited">
          <DisplayName>Unsolicited error message</DisplayName>
          <Protocol Name="Proprietary" Handler="Web.TPEngine.Providers.SelfAssertedAttributeProvider, Web.TPEngine, Version=*******, Culture=neutral, PublicKeyToken=null"/>
          <Metadata>
            <Item Key="ContentDefinitionReferenceId">api.selfasserted</Item>
            --><!-- Remove the continue button--><!--
            <Item Key="setting.showContinueButton">false</Item>
            <Item Key="setting.showCancelButton">false</Item>
          </Metadata>
          <InputClaimsTransformations>
            <InputClaimsTransformation ReferenceId="CreateUnsolicitedErrorMessage" />
          </InputClaimsTransformations>
          <InputClaims>
            <InputClaim ClaimTypeReferenceId="errorMessage"/>
          </InputClaims>
          <OutputClaims>
            <OutputClaim ClaimTypeReferenceId="errorMessage"/>
          </OutputClaims>
        </TechnicalProfile>
      </TechnicalProfiles>
    </ClaimsProvider>-->

    <!--Sample: This technical profile specifies how B2C should validate your token, and what claims you want B2C to extract from the token. 
      The METADATA value in the TechnicalProfile meta-data is required. 
      The “IdTokenAudience” and “issuer” arguments are optional (see later section)-->
    <ClaimsProvider>
      <DisplayName>My ID Token Hint ClaimsProvider</DisplayName>
      <TechnicalProfiles>
        <TechnicalProfile Id="IdTokenHint_ExtractClaims">
          <DisplayName> My ID Token Hint TechnicalProfile</DisplayName>
          <Protocol Name="None" />
          <Metadata>

            <!--Sample action required: replace with your endpoint location -->
            <!--<Item Key="METADATA">https://plxrate-dev.azurewebsites.net/.well-known/openid-configuration</Item>-->
            <Item Key="METADATA">https://smartphlexb2c.b2clogin.com/smartphlexb2c.onmicrosoft.com/B2C_1A_signin_signature/v2.0/.well-known/openid-configuration</Item>
            <!-- <Item Key="IdTokenAudience">your_optional_audience_override</Item> -->
            <!-- <Item Key="issuer">your_optional_token_issuer_override</Item> -->
          </Metadata>
          <OutputClaims>
            <!-- Read the email cliam from the id_token_hint-->
            <OutputClaim ClaimTypeReferenceId="signInName" />
          </OutputClaims>
        </TechnicalProfile>
      </TechnicalProfiles>
    </ClaimsProvider>
  </ClaimsProviders>

  <UserJourneys>
    <UserJourney Id="SignaturePlx">
      <OrchestrationSteps>

        <!-- Read the input claims from the id_token_hint-->
        <OrchestrationStep Order="1" Type="GetClaims" CpimIssuerTechnicalProfileReferenceId="IdTokenHint_ExtractClaims" />

        <!-- Check if user tries to run the policy without invitation -->
        <!--<OrchestrationStep Order="2" Type="ClaimsExchange">
          <Preconditions>
            <Precondition Type="ClaimsExist" ExecuteActionsIf="true">
              <Value>signInName</Value>
              <Action>SkipThisOrchestrationStep</Action>
            </Precondition>
          </Preconditions>
          <ClaimsExchanges>
            <ClaimsExchange Id="SelfAsserted-Unsolicited" TechnicalProfileReferenceId="SelfAsserted-Unsolicited"/>
          </ClaimsExchanges>
        </OrchestrationStep>-->

        <!-- Self-asserted sign-up page -->
        <OrchestrationStep Order="2" Type="ClaimsExchange">
          <ClaimsExchanges>
            <ClaimsExchange Id="PharmaLex-OpenIdConnect" TechnicalProfileReferenceId="PharmaLex-OpenIdConnect"/>
          </ClaimsExchanges>
        </OrchestrationStep>
		
		<OrchestrationStep Order="3" Type="ClaimsExchange">
          <ClaimsExchanges>
             <!--create the emails claim combining signInNames and otherMails--> 
            <ClaimsExchange Id="AADUserCreateEmailsClaim" TechnicalProfileReferenceId="AAD-UserCreateEmailsClaim" />
          </ClaimsExchanges>
        </OrchestrationStep>

        <!-- Issue an access token-->
        <OrchestrationStep Order="4" Type="SendClaims" CpimIssuerTechnicalProfileReferenceId="JwtIssuer"/>

      </OrchestrationSteps>
      <ClientDefinition ReferenceId="DefaultWeb"/>
    </UserJourney>
  </UserJourneys>

  <RelyingParty>
    <DefaultUserJourney ReferenceId="SignaturePlx" />
    <TechnicalProfile Id="PolicyProfile">
      <DisplayName>PolicyProfile</DisplayName>
      <Protocol Name="OpenIdConnect" />
      <InputClaims>
        <InputClaim ClaimTypeReferenceId="signInName" PartnerClaimType="signInName" />
      </InputClaims>
      <OutputClaims>
        <!--<OutputClaim ClaimTypeReferenceId="emails" />-->
        <OutputClaim ClaimTypeReferenceId="name"/>
		<OutputClaim ClaimTypeReferenceId="trustFrameworkPolicy" Required="true" DefaultValue="{policy}"/>
		<OutputClaim ClaimTypeReferenceId="identityProvider" />
		<OutputClaim ClaimTypeReferenceId="givenName" />
		<OutputClaim ClaimTypeReferenceId="surname" />
        <OutputClaim ClaimTypeReferenceId="objectId" PartnerClaimType="sub"/>
        <OutputClaim ClaimTypeReferenceId="tenantId" AlwaysUseDefaultValue="true" DefaultValue="{Policy:TenantObjectId}" />
      </OutputClaims>
      <SubjectNamingInfo ClaimType="sub" />
    </TechnicalProfile>
  </RelyingParty>

</TrustFrameworkPolicy>
