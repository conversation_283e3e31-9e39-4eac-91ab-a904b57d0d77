﻿using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authentication.OpenIdConnect;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Microsoft.Identity.Web;
using Microsoft.IdentityModel.Protocols.OpenIdConnect;
using System;
using System.Collections.Generic;

namespace PharmaLex.Authentication.B2C
{
    public static class ServiceCollectionExtensions
    {
        public static IServiceCollection ConfigureMicrosoftItentityWebAuthentication(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddRazorPages();
            services.AddSingleton<IConfigureOptions<MicrosoftIdentityOptions>, ConfigureMicrosoftIdentityOptions>();
            services.AddSingleton<IIdentityProviderEventCallbacks, IdentityProviderEventCallbacks>();
            services.AddMicrosoftIdentityWebAppAuthentication(configuration,Constants.AzureAdB2C);

            return services;
        }

        public static IServiceCollection ConfigureCustomAuthentication(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddScoped<IOidcService, OidcService>();

            var authentication = services.AddAuthentication(options =>
                {
                    options.DefaultScheme = CookieAuthenticationDefaults.AuthenticationScheme;
                    options.DefaultChallengeScheme = OpenIdConnectDefaults.AuthenticationScheme;
                });

            services.Configure<AzureAdB2CPolicyOptions>(configuration.GetSection("AzureAdB2CPolicy"));

            List<PolicyOptions> policies = new List<PolicyOptions>();
            configuration.GetSection("AzureAdB2CPolicy:Policies").Bind(policies);
            foreach (PolicyOptions policy in policies)
            {
                authentication.AddOpenIdConnect(policy.PolicyId, options => { });
                authentication.Services.AddOptions<OpenIdConnectOptions>(policy.PolicyId)
                    .Configure<IServiceProvider, IOptions<AzureAdB2CPolicyOptions>>((options, serviceProvider, azureAdPolicyOptions) =>
                    {
                        var policyOptions = azureAdPolicyOptions.Value;

                        options.MetadataAddress = policyOptions.GetMetadataEndpoint(policy.PolicyId);
                        options.ClientId = policyOptions.ClientId;
                        options.ResponseType = OpenIdConnectResponseType.IdToken;
                        options.SignedOutCallbackPath = $"/signout/{policy.PolicyId}";

                        options.CallbackPath = policy.CallbackPath;
                        options.SignInScheme = CookieAuthenticationDefaults.AuthenticationScheme;
                        options.TokenValidationParameters.NameClaimType = ClaimConstants.Email;

                        var providerEvents = serviceProvider.GetRequiredService<IIdentityProviderEventCallbacks>();
                        options.Events.OnTicketReceived = async context => await providerEvents.OnTicketReceived(context);
                        options.Events.OnRedirectToIdentityProvider = async context => await providerEvents.OnRedirectToIdentityProvider(context);
                    });
            }

            return services;
        }
    }
}
