### pharmalex.AuthenticationB2C

[![Build Status](https://dev.azure.com/Phlexglobal/Phlex.Core/_apis/build/status/PharmaLex.Authentication.B2C?repoName=PharmaLex.Authentication.B2C&branchName=master)](https://dev.azure.com/Phlexglobal/Phlex.Core/_build/latest?definitionId=1864&repoName=PharmaLex.Authentication.B2C&branchName=master)

# Description
*Pharmalex.AuthenticationB2C* library contains method to authenticate user and access microsoft graph services for use in multiple applications.

##Structure layout of projects

```
├───src
│   └───PharmaLex.Authentication.B2C
│       │   B2CFeatureProvider.cs
│       │   ClaimConstants.cs
│       │   ClaimsPrincipalExtensions.cs
│       │   DigitalSignatureAttribute.cs
│       │   logo.png
│       │   PharmaLex.Authentication.B2C.csproj
│       │   PharmaLex.Authentication.B2C.sln
│       │
│       ├───B2C
│       │       AzureAdB2CPolicyOptions.cs
│       │       ConfigureMicrosoftIdentityOptions.cs
│       │       MvcBuilderExtensions.cs
│       │       ServiceCollectionExtensions.cs
│       │
│       │
│       ├───Controllers
│       │       OidcController.cs
│       │
│       ├───Graph
│       │       AzureAdB2CGraphClientProvider.cs
│       │       AzureAdB2CGraphService.cs
│       │       AzureAdGraphClientProvider.cs
│       │       AzureAdGraphService.cs
│       │       GraphClientProvider.cs
│       │       GraphOptions.cs
│       │       GraphService.cs
│       │       UserExtensions.cs
│       │
│       ├───IDPEvents
│       │       IdentityProviderEventCallbacks.cs
│       │       ITicketReceivedCallback.cs
│       │
│       │
│       ├───Oidc
│       │       JwksModel.cs
│       │       OidcModel.cs
│       │       OidcService.cs
│       │
│       └───Policies
│               SignInDigitalSignature.xml
│               SignInDigitalSignaturePlx.xml
│               SignUpInvitation.xml
│               SignUpInvitationDev.xml
│               TrustFrameworkBase.xml
│               TrustFrameworkExtensions.xml
│
└───test
    └───PharmaLex.Authentication.B2C.Tests
        │──   PharmaLex.Authentication.B2C.Tests.csproj
``` 

## Getting started

### Local execution and Prerequisites
1. The PharmaLex NuGet pakage source should be configured in your Visual Studio instance [-------- link to Plx Package feed](https://dev.azure.com/pharmalex/Packages/_packaging?_a=feed&feed=PharmaLex.Packages).

### Instaling from NuGet

The recommended way for using the code is as a NuGet package. Two ways to do this exist:
1. Using Visual Studio - [Install and use a package in Visual Studio](https://docs.microsoft.com/en-us/nuget/quickstart/install-and-use-a-package-in-visual-studio)
2. Using the package manager console. Make sure to target the correct project.
Install-Package PharmaLex.AuthenticationB2C


### Basic usage
This library provides methods to access Microsoft Graph SDK service. it is used to get The client class and request microsoft graph api.

# Usage from application code

Implementations of the **IAzureAdB2CGraphService** should be present in the application web project. For example,here are the methods to manage users and how to use them:

1. Find a user and return it to the client: 
```
var users = await azureAdB2CGraphService.FindUsers(term);
```
2. Get the filtered Users:
```
	var filteredUsers = await azureAdB2CGraphService.GetFilteredUsers(filter);
```
3. Check a user exists:
```
var userExists = await azureAdB2CGraphService.UserExists(email);
```

Implementations of the **IAzureAdGraphService** should be present in the application web project. For example:

1. Find a user and return it to the client: 
```
var users = await azureAdGraphService.FindUsers(term);
```
2. Get the filtered Users:
```
	var filteredUsers = await azureAdGraphService.GetFilteredUsers(filter);
```
3. Check a user exists:
```
var userExists = await azureAdGraphService.UserExists(email);
```
3. Get a user picture:
```
var userPic = await azureAdGraphService.GetUserPicture(email);
```
## Version history with breaking changes

1. Migrate from Asp.Net Core 6 to 8
2. removed unused package Microsoft.AspNetCore.Authentication.Cookies 
3. upgrade Microsoft.AspNetCore.Authentication.OpenIdConnect  5.0.8 to 8.0.0
4. upgrade Microsoft.Extensions.Caching.Abstractions from 5.0.0 to 8.0.0
5. upgrade Microsoft.Extensions.Options 5.0.0 to 8.0.0
6. upgrade Microsoft.Identity.Web 1.5.1 to 2.16.0
7. upgrade Microsoft.Identity.Web.UI 1.5.1 to 2.16.0
8. upgrade Microsoft.IdentityModel.Tokens 6.12.0 to 7.0.3
9. upgrade System.IdentityModel.Tokens.Jwt 6.12.0 to 7.0.03

# Breaking Changes: 
breaking changes were comeup after upgradation of microsoft.graph. with existing package we were querying using Request() 
which is no more in latest version of microsoft.graph.
1. Changes in QueryParameters
UpdatedFile:- AzureAdGraphService.cs,AzureAdB2CGraphService.cs
https://learn.microsoft.com/en-us/graph/query-parameters?tabs=http
UpdatedFile: UserExtensions.cs
UpdatedMethod: GetAllAsync()
breaking changes after upgradation of microsoft.graph 3.3.5 to 5.38.0
change:- As this method is an extension method of GraphServiceUsersCollectionRequest class after upgradation this class doen't exist
so we have change this method to normal method. which takes UserCollectionResponse users, GraphServiceClient client parameters. 
documentation reference:-https://learn.microsoft.com/en-us/graph/sdks/paging?tabs=csharp#manually-requesting-subsequent-pages
2. Authenticate graph service and access GraphServiceClient
as per existing version of microsoft.graph there was a code generate token and authenticate graph service 
which is no longer use after package upgradation 
for eg:- with the latest version ClientCredentialProvider class doesn't exist hence we have updated code according to the 
latest version.
https://learn.microsoft.com/en-us/graph/tutorials/dotnet-app-only?tabs=aad&tutorial-step=3

