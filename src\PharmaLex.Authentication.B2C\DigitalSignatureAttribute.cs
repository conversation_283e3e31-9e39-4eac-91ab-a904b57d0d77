﻿using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Mvc.Filters;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace PharmaLex.Authentication.B2C
{
    public class DigitalSignatureAttribute : Attribute, IAsyncResourceFilter
    {
        private readonly int _secondsFromLastLogin;
        public DigitalSignatureAttribute(int secondsFromLastLogin = 5)
        {
            _secondsFromLastLogin = secondsFromLastLogin;
        }
        public async Task OnResourceExecutionAsync(ResourceExecutingContext context, ResourceExecutionDelegate next)
        {
            var foundAuthTime = int.TryParse(context.HttpContext.User.FindFirst(ClaimConstants.AuthenticationTime)?.Value, out int authTime);

            if (foundAuthTime && new DateTimeOffset(DateTime.UtcNow).ToUnixTimeSeconds() - authTime < _secondsFromLastLogin)
            {
                await next();
            }
            else
            {
                var policyId = context.HttpContext.User.IsLocalUser()? UserFlows.SignInLocal: UserFlows.SignInFederated;

                var state = new Dictionary<string, string> { { "reauthenticate", "true" } };
                await context.HttpContext.ChallengeAsync(scheme: policyId, new AuthenticationProperties(state)
                {
                    RedirectUri = context.HttpContext.Request.Path
                });
            }
        }
    }
}
