﻿using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authentication.OpenIdConnect;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;

namespace PharmaLex.Authentication.B2C.Controllers
{
    public class OidcController : Controller
    {
        private readonly AzureAdB2CPolicyOptions azureAdPolicyOptions;
        private readonly IOidcService oidcService;

        public OidcController(IOptions<AzureAdB2CPolicyOptions> policyOptions, IOidcService oidcService)
        {
            this.azureAdPolicyOptions = policyOptions.Value;
            this.oidcService = oidcService;
        }

        [Route(".well-known/openid-configuration"), AllowAnonymous]
        public ActionResult Metadata()
        {
            return Content(JsonConvert.SerializeObject(new OidcModel
            {
                Issuer = this.azureAdPolicyOptions.IssuerUrl,
                JwksUri = $"{this.azureAdPolicyOptions.IssuerUrl.TrimEnd('/')}/.well-known/keys",
                IdTokenSigningAlgValuesSupported = new[] { this.oidcService.SigningCredentials.Value.Algorithm },
            }), "application/json");
        }

        [Route(".well-known/keys"), AllowAnonymous]
        public ActionResult JwksDocument()
        {
            return Content(JsonConvert.SerializeObject(new JwksModel
            {
                Keys = new[] { JwksKeyModel.FromSigningCredentials(this.oidcService.SigningCredentials.Value) }
            }), "application/json");
        }

        [Route("/signin"), AllowAnonymous]
        public IActionResult PlxLogin()
        {
            var properties = new AuthenticationProperties { RedirectUri = "/" };
            var policyOptions = this.oidcService.GetPolicyOptionsByRequestPath(this.Request.Path);

            return this.Challenge(properties, policyOptions.PolicyId);
        }

        [Route("/signout")]
        public IActionResult Signout()
        {
            var properties = new AuthenticationProperties { RedirectUri = "/" };

            return this.SignOut(properties,
                CookieAuthenticationDefaults.AuthenticationScheme,
                OpenIdConnectDefaults.AuthenticationScheme);
        }

        [HttpGet("/signup-invitation"), AllowAnonymous]
        public IActionResult SignUpInvitation(string id_token_hint)
        {
            var properties = new AuthenticationProperties { RedirectUri = "/" };
            properties.Items.Add("id_token_hint", id_token_hint);

            var policyOptions = this.oidcService.GetPolicyOptionsByRequestPath(this.Request.Path);

            return this.Challenge(properties, policyOptions.PolicyId);
        }
    }
}
