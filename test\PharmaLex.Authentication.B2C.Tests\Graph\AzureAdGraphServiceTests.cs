﻿using Microsoft.Graph.Models;
using Microsoft.Graph;
using Microsoft.Kiota.Abstractions.Serialization;
using Microsoft.Kiota.Abstractions;
using Xunit;
using NSubstitute;

namespace PharmaLex.Authentication.B2C.Tests.Graph
{
    public class AzureAdGraphServiceTests
    {
        private readonly IAzureAdGraphService azureAdGraphService;
        private readonly IGraphClientProvider<AzureAdGraphOptions> clientProvider;

        public AzureAdGraphServiceTests()
        {
            azureAdGraphService = Substitute.For<IAzureAdGraphService>();
            clientProvider = Substitute.For<IGraphClientProvider<AzureAdGraphOptions>>();
            azureAdGraphService = new AzureAdGraphService(clientProvider);
        }
        [Fact]
        public void UserExists_Returns_Fault()
        {
            var requestAdapter = Substitute.For<IRequestAdapter>();
            GraphServiceClient graphClient = new GraphServiceClient(requestAdapter);

            var usersMock = new UserCollectionResponse()
            {
                Value = new List<User> {
                new User()
                 {
                     Id = Guid.NewGuid().ToString(),
                     GivenName = "John",
                     Surname = "Doe",
                 },
                 new User()
                 {
                     Id = Guid.NewGuid().ToString(),
                     GivenName = "Jane",
                     Surname = "Doe"
                 }
                }
            };

            requestAdapter.SendAsync(
                Arg.Any<RequestInformation>(),
                Arg.Any<ParsableFactory<UserCollectionResponse>>(),
                Arg.Any<Dictionary<string, ParsableFactory<IParsable>>>(),
                Arg.Any<CancellationToken>())
                .ReturnsForAnyArgs(usersMock);

            graphClient.Users.GetAsync().ReturnsForAnyArgs(usersMock);
            var res = azureAdGraphService.UserExists("useremail");
            Assert.True(res.IsFaulted);
        }

        [Fact]
        public void FindUsers_Returns_Fault()
        {
            var requestAdapter = Substitute.For<IRequestAdapter>();
            GraphServiceClient graphClient = new GraphServiceClient(requestAdapter);

            var usersMock = new UserCollectionResponse()
            {
                Value = new List<User> {
                    new User()
                    {
                        Id = Guid.NewGuid().ToString(),
                        GivenName = "John",
                        Surname = "Doe",
                    },
                    new User()
                    {
                        Id = Guid.NewGuid().ToString(),
                        GivenName = "Jane",
                        Surname = "Doe"
                    }
                }
            };

            requestAdapter.SendAsync(
                Arg.Any<RequestInformation>(),
                Arg.Any<ParsableFactory<UserCollectionResponse>>(),
                Arg.Any<Dictionary<string, ParsableFactory<IParsable>>>(),
                Arg.Any<CancellationToken>())
                .ReturnsForAnyArgs(usersMock);

            graphClient.Users.GetAsync().ReturnsForAnyArgs(usersMock);
            var res = azureAdGraphService.FindUsers("user");
            Assert.True(res.IsFaulted);
        }
        [Fact]
        public void GetUserPicture_Returns_Exception()
        {
            var res = azureAdGraphService.GetUserPicture("useremail");
            Assert.NotNull(res.Result);
        }
    }
}
