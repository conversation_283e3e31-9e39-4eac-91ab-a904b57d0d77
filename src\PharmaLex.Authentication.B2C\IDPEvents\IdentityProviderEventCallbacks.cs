﻿using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.OpenIdConnect;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace PharmaLex.Authentication.B2C
{
    public interface IIdentityProviderEventCallbacks
    {
        Task OnRedirectToIdentityProvider(RedirectContext context);
        Task OnTicketReceived(TicketReceivedContext context);
    }

    public class IdentityProviderEventCallbacks : IIdentityProviderEventCallbacks
    {
        public async Task OnRedirectToIdentityProvider(RedirectContext context)
        {
            var b2cOptions = context.HttpContext.RequestServices.GetRequiredService<IOptions<AzureAdB2CPolicyOptions>>();
            var domains = b2cOptions.Value.Domains;
            //List<List<string>> domains = new List<List<string>>();
            //configuration.GetSection("AzureAdB2CPolicy:Domains").Bind(domains);

            foreach (var item in context.Properties.Items)
            {
                context.ProtocolMessage.SetParameter(item.Key, item.Value);
            }

            var path = context.Request.Path;
            if (!string.IsNullOrEmpty(path) && path != "/login")
            {
                context.Response.Redirect($"https://{context.Request.Host}?postLoginRedirect={path}");
                context.HandleResponse();
                await Task.CompletedTask;
            }
            else if (path == "/login")
            {
                var redirectUri = context.Request.Query.FirstOrDefault(q => q.Key == "postLoginRedirect").Value;
                if (!string.IsNullOrEmpty(redirectUri))
                {
                    context.Properties.RedirectUri = redirectUri;
                }
            }

            if (context.ShouldReauthenticate())
            {
                var email = context.HttpContext.User.GetEmail().ToLower();
                var emailDomain = email.Split('@')[1];
                context.ProtocolMessage.Prompt = "login";
                context.ProtocolMessage.DomainHint =
                    domains.FirstOrDefault(x => x.Any(y => y.ToLower() == emailDomain))?.First();
                context.ProtocolMessage.LoginHint = email;
            }

            await Task.CompletedTask;
        }

        public async Task OnTicketReceived(TicketReceivedContext context)
        {
            var customTicketReceived = context.HttpContext.RequestServices.GetService<ITicketReceivedCallback>();
            await customTicketReceived?.OnTicketReceived(context);
        }
    }

    public static class RedirectContextExtensions
    {
        internal static bool ShouldReauthenticate(this RedirectContext context)
        {
            context.Properties.Items.TryGetValue("reauthenticate", out string reauthenticate);

            bool shouldReauthenticate = false;
            if (reauthenticate != null && !bool.TryParse(reauthenticate, out shouldReauthenticate))
            {
                throw new InvalidOperationException($"'{reauthenticate}' is not a boolean value");
            }

            return shouldReauthenticate;
        }
    }
}