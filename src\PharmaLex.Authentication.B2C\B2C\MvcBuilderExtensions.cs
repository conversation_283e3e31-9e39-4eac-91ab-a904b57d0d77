﻿using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;

namespace PharmaLex.Authentication.B2C
{
    public static class MvcBuilderExtensions
    {
        public static IMvcBuilder AddAzureAdB2CAuthenticationUI(this IMvcBuilder builder)
        {
            builder.ConfigureApplicationPartManager(apm =>
            {
                apm.FeatureProviders.Add(new B2CFeatureProvider());
            });

            var services = builder.Services;
            services.Configure<CookieAuthenticationOptions>(options =>
            {
                if (string.IsNullOrEmpty(options.AccessDeniedPath))
                {
                    options.AccessDeniedPath = new PathString("/");
                }
            });

            return builder;
        }
    }
}
