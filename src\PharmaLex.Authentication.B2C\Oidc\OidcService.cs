﻿using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Claims;
using System.Security.Cryptography.X509Certificates;

namespace PharmaLex.Authentication.B2C
{
    public interface IOidcService
    {
        Lazy<X509SigningCredentials> SigningCredentials { get; }
        string GetSignupInvitationLink(string callbackPath, IDictionary<string, string> claims);
        PolicyOptions GetPolicyOptionsByRequestPath(string path);
    }

    public class OidcService : IOidcService
    {
        private readonly AzureAdB2CPolicyOptions b2cOptions;
        private readonly string issuerUrl;
        private readonly HttpContext httpContext;

        public OidcService(IOptions<AzureAdB2CPolicyOptions> b2cOptions,
            IHttpContextAccessor contextAccessor)
        {
            this.b2cOptions = b2cOptions.Value;
            this.issuerUrl = b2cOptions.Value.IssuerUrl;
            this.httpContext = contextAccessor.HttpContext;

            SigningCredentials = new Lazy<X509SigningCredentials>(() =>
            {
                X509Store certStore = new X509Store(StoreName.My, StoreLocation.CurrentUser);
                certStore.Open(OpenFlags.ReadOnly);
                X509Certificate2Collection certCollection =
                    certStore.Certificates.Find(
                        X509FindType.FindByThumbprint,
                        this.b2cOptions.SigningCertThumbprint,
                        false);

                X509Certificate2 cert = certCollection.OfType<X509Certificate2>().FirstOrDefault();

                if (cert is null)
                    throw new Exception($"Certificate with thumbprint {this.b2cOptions.SigningCertThumbprint} was not found");

                return new X509SigningCredentials(cert);
            });
        }

        public Lazy<X509SigningCredentials> SigningCredentials { get; }

        public string GetSignupInvitationLink(string callbackPath, IDictionary<string, string> claims)
        {
            HttpRequest request = this.httpContext.Request;
            PolicyOptions options = this.GetPolicyOptionsByRequestPath(callbackPath);
            return $"{request.Scheme}://{request.Host}{request.PathBase.Value}/{options.ApplicationCallbackPath.Trim('/')}" +
                $"?id_token_hint={this.BuildIdToken(claims)}";
        }

        public PolicyOptions GetPolicyOptionsByRequestPath(string path)
        {
            return this.b2cOptions.Policies.First(x => x.ApplicationCallbackPath.EndsWith(path));
        }

        private string BuildIdToken(IDictionary<string, string> claims)
        {
            JwtSecurityToken token = new JwtSecurityToken(
                this.issuerUrl,
                this.b2cOptions.ClientId,
                claims.Select(x => new Claim(x.Key, x.Value, ClaimValueTypes.String, this.issuerUrl)),
                DateTime.Now,
                DateTime.Now.AddDays(this.b2cOptions.LinkExpiresAfterDays),
                this.SigningCredentials.Value);

            JwtSecurityTokenHandler jwtHandler = new JwtSecurityTokenHandler();

            return jwtHandler.WriteToken(token);
        }
    }
}
