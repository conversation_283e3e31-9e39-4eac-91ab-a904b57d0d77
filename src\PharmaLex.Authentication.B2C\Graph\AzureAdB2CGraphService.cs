﻿using Microsoft.Graph;
using Microsoft.Graph.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace PharmaLex.Authentication.B2C
{
    public class AzureAdB2CGraphService : IAzureAdB2CGraphService
    {
        private readonly GraphServiceClient graphClient;
        private readonly string tenant;
        private static readonly string[] selectQueryparametersForFilteredUsers = ["Id", "OtherMails", "Identities", "Surname", "GivenName"];
        public AzureAdB2CGraphService(IGraphClientProvider<AzureAdB2CGraphOptions> clientProvider)
        {
            this.graphClient = clientProvider.Client;
            this.tenant = clientProvider.Tenant;
        }

        public async Task<List<User>> FindUsers(string term)
        {
            //return only local accounts
            return (await this.GetFilteredUsers($"startsWith(givenName, '{term}') or startsWith(surname, '{term}')"))
                .Where(x => x.Identities.Exists(y => y.Issuer == this.tenant && y.SignInType == "emailAddress"))
                .Union(await this.GetFilteredUsers($"identities/any(c:c/issuerAssignedId eq '{term}' and c/issuer eq '{this.tenant}')")).Distinct().ToList();
        }

        private async Task<List<User>> GetFilteredUsers(string filter)
        {
            var result = await graphClient.Users.GetAsync((requestConfiguration) =>
            {
                requestConfiguration.QueryParameters.Select = selectQueryparametersForFilteredUsers;
                requestConfiguration.QueryParameters.Filter = filter;
            });
            return await UserExtensions.GetAllAsync(result, graphClient);
        }

        public async Task<bool> UserExists(string email)
        {
            //Local users

            var existing = await this.graphClient.Users.GetAsync(
                requestConfiguration => { requestConfiguration.QueryParameters.Filter = $"identities/any(c:c/issuerAssignedId eq '{email}' and c/issuer eq '{this.tenant}')"; }
            );
            List<User> users = await UserExtensions.GetAllAsync(existing, graphClient);
            return users.Count > 0;
        }

        public async Task<byte[]> GetUserPicture(string email)
        {
            return await Task.FromResult(Convert.FromBase64String("/9j/4AAQSkZJRgABAQEAYABgAAD/4QCMRXhpZgAATU0AKgAAAAgABwEaAAUAAAABAAAAYgEbAAUAAAABAAAAagEoAAMAAAABAAIAAAExAAIAAAARAAAAclEQAAEAAAABAQAAAFERAAQAAAABAAAAAFESAAQAAAABAAAAAAAAAAAAAABgAAAAAQAAAGAAAAABcGFpbnQubmV0IDQuMC4yMQAA/9sAQwACAQECAQECAgICAgICAgMFAwMDAwMGBAQDBQcGBwcHBgcHCAkLCQgICggHBwoNCgoLDAwMDAcJDg8NDA4LDAwM/9sAQwECAgIDAwMGAwMGDAgHCAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwM/8AAEQgAPAA8AwEiAAIRAQMRAf/EAB8AAAEFAQEBAQEBAAAAAAAAAAABAgMEBQYHCAkKC//EALUQAAIBAwMCBAMFBQQEAAABfQECAwAEEQUSITFBBhNRYQcicRQygZGhCCNCscEVUtHwJDNicoIJChYXGBkaJSYnKCkqNDU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6g4SFhoeIiYqSk5SVlpeYmZqio6Slpqeoqaqys7S1tre4ubrCw8TFxsfIycrS09TV1tfY2drh4uPk5ebn6Onq8fLz9PX29/j5+v/EAB8BAAMBAQEBAQEBAQEAAAAAAAABAgMEBQYHCAkKC//EALURAAIBAgQEAwQHBQQEAAECdwABAgMRBAUhMQYSQVEHYXETIjKBCBRCkaGxwQkjM1LwFWJy0QoWJDThJfEXGBkaJicoKSo1Njc4OTpDREVGR0hJSlNUVVZXWFlaY2RlZmdoaWpzdHV2d3h5eoKDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uLj5OXm5+jp6vLz9PX29/j5+v/aAAwDAQACEQMRAD8A/aCiiitDMKK4P4t/F/8A4QlvsNiscupOu5mblbcHpkd2PYfjXkd/8Qdc1O4Mk2rX5YnOFmZFH0C4A/CnygfTFNd9orwXwd8ctY8N3KLeTSalZ5w6THdIB6q3XP1yP517Tp2uW+vaZDeWsnmW9wodG9v/AK3SkBPPNVN7jDUXM9U3uPmoA6SiiigD5f8AFepyax4mv7qUkvNO7c9hk4H4DA/Cs8nFdN8WfCMvhLxldKVP2a6dp4HxwVY5I/AnH5etcrLJWgDZZM163+zvrEk3hm+tWYsttOGTPYMOR+Yz+NeOySYr2v4O+G5PC3g4NcKUuL5/PZSOUXGFB/Dn8aUgOturjFZ0t189LeXWM1mTXWJKgD0iiivNfj/8SJvD9tHpNjI0dxdJvnkU/NHGeAB6E8/gPegC78UfH3hOS2bTdUY37K3KW67nhb1DZABHpn6ivNrjwV4X1A+ZaeKlt4zz5dzbHev45XP4CuLkkqrPLV2A9A0S78E+CtUikkurrWJ1ORL5GIYT67Tyf/Hq9IsfE9n4isRcWNzHcQtxlD0PoR1B9jXzTcT1N4Y8bXXg3WEurZzt4EsWfllXuD/Q9qTQH0Je3WO9Zc95iQ/NUUeuR6tp8N1C26K4QSIfYjNVGmy1SB7WzbRXzb8ZL9734k6qzH7sojA9AqgD+VfRtwxBr5n+K7f8XC1f/r4b+lVEDm5pcCqdxNUly55rPuHOKoCO6nxms66uKluXPNZ125z+FAHsvwf1BrrwFbqxz5Mjov0zn+tdA0+D2rkfgs3/ABQi/wDXd/6V1mKlgf/Z"));
        }
    }
}
