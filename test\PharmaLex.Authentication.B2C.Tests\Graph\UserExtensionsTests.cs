﻿using Microsoft.Graph.Models;
using Xunit;

namespace PharmaLex.Authentication.B2C.Tests.Graph;
public class UserExtensionsTests
{
    private const string UserPrincipalName = "<EMAIL>";
    private const string EmailAddress = "<EMAIL>";
    private const string IdentityEmailAddress = "<EMAIL>";
    private const string IdentityUserPrincipalName = "<EMAIL>";
    private const string DuffValue = "*** should not be this value ***";

    [Fact]
    public void Given_user_has_a_userPrincipalName_Then_userPrincipalName_is_returned()
    {
        // Arrange
        var user = new User
        {
            UserPrincipalName = UserPrincipalName,
        };

        // Act
        var result = user.GetEmail();

        // Assert
        Assert.Equal(UserPrincipalName, result);
    }

    [Fact]
    public void Given_user_does_not_have_a_userPrincipalName_or_identities_Then_email_address_is_returned()
    {
        // Arrange
        var user = new User
        {
            UserPrincipalName = string.Empty,
            Mail = EmailAddress,
        };

        // Act
        var result = user.GetEmail();

        // Assert
        Assert.Equal(EmailAddress, result);
    }

    [Fact]
    public void Given_user_does_not_have_a_userPrincipalName_but_has_identities_but_not_emailAddress_or_userPrincipalName_Then_email_address_is_returned()
    {
        // Arrange
        var user = new User
        {
            UserPrincipalName = string.Empty,
            Identities =
            [
                new ObjectIdentity { SignInType = "notEmailAddress", IssuerAssignedId = DuffValue },
                new ObjectIdentity { SignInType = "notUserPrincipalName", IssuerAssignedId = DuffValue }
            ],
            Mail = EmailAddress,
        };

        // Act
        var result = user.GetEmail();

        // Assert
        Assert.NotEqual(DuffValue, result);
        Assert.Equal(EmailAddress, result);
    }

    [Fact]
    public void Given_user_does_not_have_a_userPrincipalName_but_has_identity_for_emailAddress_Then_email_address_is_returned()
    {
        // Arrange
        var user = new User
        {
            UserPrincipalName = string.Empty,
            Identities =
            [
                new ObjectIdentity { SignInType = "emailAddress", IssuerAssignedId = IdentityEmailAddress },
                new ObjectIdentity { SignInType = "notUserPrincipalName", IssuerAssignedId = DuffValue }
            ]
        };

        // Act
        var result = user.GetEmail();

        // Assert
        Assert.NotEqual(DuffValue, result);
        Assert.Equal(IdentityEmailAddress, result);
    }

    [Fact]
    public void Given_user_does_not_have_a_userPrincipalName_but_has_identity_for_userPrincipalName_Then_UserPrincipalName_is_returned()
    {
        // Arrange
        var user = new User
        {
            UserPrincipalName = string.Empty,
            Identities =
            [
                new ObjectIdentity { SignInType = "notEmailAddress", IssuerAssignedId = DuffValue },
                new ObjectIdentity { SignInType = "userPrincipalName", IssuerAssignedId = IdentityUserPrincipalName }
            ]
        };

        // Act
        var result = user.GetEmail();

        // Assert
        Assert.NotEqual(DuffValue, result);
        Assert.Equal(IdentityUserPrincipalName, result);
    }
}
