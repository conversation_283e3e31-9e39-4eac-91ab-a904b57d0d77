﻿using Microsoft.Graph.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace PharmaLex.Authentication.B2C
{
    public interface IGraphService<T> where T : GraphOptions
    {
        Task<List<User>> FindUsers(string term);
        Task<bool> UserExists(string email);
        Task<byte[]> GetUserPicture(string email);
    }

    public interface IAzureAdGraphService : IGraphService<AzureAdGraphOptions> { }
    public interface IAzureAdB2CGraphService : IGraphService<AzureAdB2CGraphOptions> { }
}
