﻿using System.Collections.Generic;

namespace PharmaLex.Authentication.B2C
{
    public class AzureAdB2CPolicyOptions
    {
        public string Tenant { get; set; }
        public string TenantId { get; set; }
        public string ClientId { get; set; }
        public string SigningCertThumbprint { get; set; }
        public int LinkExpiresAfterDays { get; set; }
        public List<PolicyOptions> Policies { get; set; }
        public List<List<string>> Domains { get; set; } = new List<List<string>>();

        public string IssuerUrl => $"https://{this.Tenant}.b2clogin.com/{this.TenantId}/v2.0/";

        public string GetMetadataEndpoint(string policyId) => $"https://{this.Tenant}.b2clogin.com/{this.Tenant}.onmicrosoft.com/v2.0/.well-known/openid-configuration?p={policyId}";
    }

    public class PolicyOptions
    {
        private string _policyId;
        public string PolicyId { get => _policyId; set => _policyId = (value ?? string.Empty).ToLower(); }
        public string ApplicationCallbackPath { get; set; } = string.Empty;
        public string CallbackPath { get; set; }

    }
}
